# Fastlane Apple Sign-In Automation Setup Guide

This comprehensive guide covers the complete setup and usage of Fastlane automation for Apple Sign-In configuration in the Bloomg Flutter application.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Available Lanes](#available-lanes)
6. [Usage Examples](#usage-examples)
7. [CI/CD Integration](#cicd-integration)
8. [Troubleshooting](#troubleshooting)
9. [Security Best Practices](#security-best-practices)

## Overview

The Fastlane automation system provides:

- **Automated iOS capability management** for Apple Sign-In across all environments
- **Apple Developer Portal integration** for bundle ID and service configuration
- **Cross-platform configuration** for iOS, Android, and Web platforms
- **Comprehensive testing automation** with Playwright and Flutter integration tests
- **CI/CD pipeline integration** with Bitbucket Pipelines

### Supported Environments

- **Production**: `com.algomash.radiance`
- **Staging**: `com.algomash.radiance.stg`
- **Development**: `com.algomash.radiance.dev`

## Prerequisites

### Required Tools

1. **Ruby** (2.7 or later)
2. **Bundler** gem
3. **Xcode** (latest version)
4. **Flutter** SDK
5. **Node.js** and npm (for Playwright tests)

### Required Accounts and Access

1. **Apple Developer Account** with Admin role
2. **App Store Connect** access
3. **Firebase Console** access to `bloomg-flutter` project
4. **Bitbucket** repository access for CI/CD

### Apple Developer Portal Setup

1. **Team ID**: `598AQBZ36R`
2. **Apple Sign-In Service ID**: `com.algomash.bloomg.bloomg-flutter`
3. **Bundle IDs**: Must be registered for all three environments
4. **Certificates**: Development and distribution certificates
5. **Provisioning Profiles**: For each environment

## Installation

### 1. Install Ruby Dependencies

```bash
# Navigate to project root
cd /path/to/bloomg_flutter

# Install Fastlane and dependencies
cd fastlane
bundle install
```

### 2. Install Node.js Dependencies

```bash
# Install Playwright for web testing
npm install
npx playwright install
```

### 3. Verify Installation

```bash
# Check Fastlane installation
bundle exec fastlane --version

# Check available lanes
bundle exec fastlane ios
```

## Configuration

### 1. Environment Variables

Create a `.env` file in the project root (add to `.gitignore`):

```bash
# Apple Developer Portal
APPLE_ID=<EMAIL>
APPLE_TEAM_ID=598AQBZ36R

# App Store Connect API
APP_STORE_CONNECT_API_KEY_ID=ABC123DEFG
APP_STORE_CONNECT_API_ISSUER_ID=12345678-1234-1234-1234-123456789012
APP_STORE_CONNECT_API_KEY_CONTENT=LS0tLS1CRUdJTi...base64-encoded-content...

# iTunes Connect (if different)
ITUNES_CONNECT_TEAM_ID=598AQBZ36R

# Optional: Enhanced security
FASTLANE_PASSWORD=your-apple-id-password
FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=app-specific-password
```

### 2. Load Environment Variables

```bash
# Load variables before running Fastlane
source .env

# Or export individually
export APPLE_ID="<EMAIL>"
export APPLE_TEAM_ID="598AQBZ36R"
# ... etc
```

### 3. Verify Configuration

```bash
# Test configuration
bundle exec fastlane ios show_apple_signin_status
```

## Available Lanes

### iOS Platform Lanes

#### Core Setup Lanes

```bash
# Setup Apple Sign-In for specific environment
bundle exec fastlane ios setup_apple_signin environment:production
bundle exec fastlane ios setup_apple_signin environment:staging
bundle exec fastlane ios setup_apple_signin environment:development

# Setup for all environments
bundle exec fastlane ios setup_apple_signin_all_environments
```

#### Capability Management

```bash
# Enable iOS Apple Sign-In capability
bundle exec fastlane ios enable_ios_apple_signin_capability environment:production

# Configure bundle ID capabilities in Developer Portal
bundle exec fastlane ios configure_bundle_id_capabilities environment:production

# Register bundle ID if needed
bundle exec fastlane ios register_bundle_id_if_needed bundle_id:com.algomash.radiance name:"Bloomg Flutter Production"
```

#### Build and Testing

```bash
# Build for testing
bundle exec fastlane ios build_for_testing environment:development

# Setup certificates and profiles
bundle exec fastlane ios setup_certificates_and_profiles environment:production

# Run iOS simulator tests
bundle exec fastlane ios run_ios_simulator_tests environment:development
```

### Cross-Platform Lanes

#### Android Platform

```bash
# Setup Apple Sign-In for Android (web authentication)
bundle exec fastlane android setup_android_apple_signin

# Validate Android Firebase configuration
bundle exec fastlane android validate_android_firebase_config
```

#### Web Platform

```bash
# Setup Apple Sign-In for Web platform
bundle exec fastlane setup_web_apple_signin

# Validate web configuration
bundle exec fastlane validate_web_apple_signin_config
```

#### Multi-Platform Setup

```bash
# Complete setup for all platforms and environments
bundle exec fastlane setup_apple_signin_complete

# Validate complete setup
bundle exec fastlane validate_complete_apple_signin_setup
```

### Testing and Validation Lanes

```bash
# Run web authentication tests
bundle exec fastlane ios run_web_authentication_tests

# Validate Apple Sign-In setup
bundle exec fastlane ios validate_apple_signin_setup environment:production

# Test Apple Sign-In functionality
bundle exec fastlane ios test_apple_signin environment:development
```

### Utility Lanes

```bash
# Show configuration status
bundle exec fastlane ios show_apple_signin_status

# Clean configuration
bundle exec fastlane ios clean_apple_signin_config

# Prepare for deployment
bundle exec fastlane prepare_for_deployment environment:production

# Post-deployment validation
bundle exec fastlane post_deployment_validation environment:production
```

## Usage Examples

### Development Workflow

```bash
# 1. Setup development environment
bundle exec fastlane ios setup_apple_signin environment:development

# 2. Build and test
bundle exec fastlane ios build_for_testing environment:development
bundle exec fastlane ios test_apple_signin environment:development

# 3. Validate setup
bundle exec fastlane ios validate_apple_signin_setup environment:development
```

### Production Deployment

```bash
# 1. Clean previous configuration
bundle exec fastlane ios clean_apple_signin_config

# 2. Setup production environment
bundle exec fastlane ios setup_apple_signin environment:production

# 3. Prepare for deployment
bundle exec fastlane prepare_for_deployment environment:production

# 4. Post-deployment validation
bundle exec fastlane post_deployment_validation environment:production
```

### Complete Multi-Platform Setup

```bash
# Setup everything at once
bundle exec fastlane setup_apple_signin_complete

# Validate everything
bundle exec fastlane validate_complete_apple_signin_setup
```

### Testing Only

```bash
# Run comprehensive tests
./scripts/run_apple_signin_tests.sh

# Run specific test types
bundle exec fastlane ios run_web_authentication_tests
npx playwright test tests/apple_signin_web_test.spec.js
flutter test integration_test/apple_signin_test.dart
```

## CI/CD Integration

### Bitbucket Pipelines

The project includes automated Apple Sign-In setup in Bitbucket Pipelines:

```yaml
# Main branch: Complete setup
- step: *setup-apple-signin
- step: *flutter-test
- step: *flutter-build-android
- step: *flutter-build-ios

# Development branch: Development environment
- step: Setup Apple Sign-In for Development
- step: Build Development APK

# Staging branch: Staging environment
- step: Setup Apple Sign-In for Staging
- step: Build Staging APK
```

### Environment Variables in CI

Set these in Bitbucket repository settings:

- `APPLE_ID` (secured)
- `APPLE_TEAM_ID`
- `APP_STORE_CONNECT_API_KEY_ID` (secured)
- `APP_STORE_CONNECT_API_ISSUER_ID` (secured)
- `APP_STORE_CONNECT_API_KEY_CONTENT` (secured)

### Manual CI Trigger

```bash
# Trigger specific environment setup in CI
git push origin feature/apple-signin-setup

# The pipeline will automatically:
# 1. Setup Apple Sign-In configuration
# 2. Run tests
# 3. Build applications
# 4. Validate setup
```

## Troubleshooting

### Common Issues

#### 1. Authentication Errors

```bash
# Error: Invalid Apple ID credentials
# Solution: Verify APPLE_ID and password
export APPLE_ID="<EMAIL>"
export FASTLANE_PASSWORD="correct-password"
```

#### 2. API Key Issues

```bash
# Error: Invalid API key
# Solution: Verify base64 encoding
base64 -i AuthKey_ABC123DEFG.p8 | tr -d '\n'
```

#### 3. Bundle ID Not Found

```bash
# Error: App with bundle ID not found
# Solution: Register bundle ID first
bundle exec fastlane ios register_bundle_id_if_needed bundle_id:com.algomash.radiance.dev name:"Bloomg Flutter Development"
```

#### 4. Provisioning Profile Issues

```bash
# Error: No matching provisioning profile
# Solution: Clean and regenerate profiles
bundle exec fastlane ios clean_apple_signin_config
bundle exec fastlane ios setup_certificates_and_profiles environment:development
```

### Debug Commands

```bash
# Enable verbose logging
export FASTLANE_VERBOSE=1

# Test API connection
bundle exec fastlane spaceship

# Validate specific environment
bundle exec fastlane ios validate_apple_signin_setup environment:development

# Show detailed status
bundle exec fastlane ios show_apple_signin_status
```

### Log Analysis

```bash
# Check Fastlane logs
tail -f ~/.fastlane/logs/fastlane.log

# Check specific lane logs
bundle exec fastlane ios setup_apple_signin environment:development --verbose
```

## Security Best Practices

### 1. Credential Management

- **Never commit** `.p8` files or credentials to version control
- **Use environment variables** for all sensitive data
- **Rotate API keys** regularly (every 6-12 months)
- **Limit API key permissions** to minimum required

### 2. Access Control

- **Use App Store Connect API keys** instead of personal credentials
- **Implement least privilege** access for team members
- **Monitor API key usage** in App Store Connect
- **Revoke unused keys** immediately

### 3. CI/CD Security

- **Use secured environment variables** in Bitbucket
- **Limit pipeline access** to authorized team members
- **Audit pipeline changes** regularly
- **Use separate keys** for different environments if needed

### 4. Local Development

```bash
# Use .env file (add to .gitignore)
echo ".env" >> .gitignore

# Set restrictive permissions
chmod 600 .env

# Use app-specific passwords
# Generate at: https://appleid.apple.com/account/manage
```

## Support and Resources

### Documentation Links

- [Fastlane Documentation](https://docs.fastlane.tools/)
- [App Store Connect API](https://developer.apple.com/documentation/appstoreconnectapi)
- [Apple Sign-In Documentation](https://developer.apple.com/documentation/sign_in_with_apple)
- [Firebase Auth Documentation](https://firebase.google.com/docs/auth)

### Getting Help

1. **Check logs** for detailed error messages
2. **Verify configuration** with status commands
3. **Test manually** in Apple Developer Portal
4. **Contact team lead** for credential issues
5. **Check Apple Developer Support** for account-specific problems

### Useful Commands Reference

```bash
# Quick setup for development
bundle exec fastlane ios setup_apple_signin environment:development

# Complete validation
bundle exec fastlane validate_complete_apple_signin_setup

# Emergency cleanup
bundle exec fastlane ios clean_apple_signin_config

# Status check
bundle exec fastlane ios show_apple_signin_status

# Full test run
./scripts/run_apple_signin_tests.sh
```
