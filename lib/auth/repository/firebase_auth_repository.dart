import 'dart:async';
import 'dart:io';

import 'package:bloomg_flutter/auth/models/user_model.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/core/storage/hive_service.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

/// {@template firebase_auth_repository}
/// Repository which manages user authentication using Firebase Auth.
/// {@endtemplate}
class FirebaseAuthRepository implements AuthRepository {
  /// {@macro firebase_auth_repository}
  FirebaseAuthRepository({
    FirebaseAuth? firebaseAuth,
    HiveService? hiveService,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _hiveService = hiveService ?? HiveService();

  final FirebaseAuth _firebaseAuth;
  final HiveService _hiveService;
  final LoggerService _logger = LoggerService();
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  /// Stream of [UserModel] which will emit the current user when
  /// the authentication state changes.
  ///
  /// Emits [UserModel.empty] if the user is not authenticated.
  @override
  Stream<UserModel> get user {
    return _firebaseAuth.authStateChanges().map((firebaseUser) {
      final user = firebaseUser == null
          ? const UserModel.empty()
          : _mapFirebaseUserToUserModel(firebaseUser);

      // Save user to local storage when authenticated
      if (user.isNotEmpty) {
        _hiveService.saveUser(user).catchError((Object error) {
          _logger.error(
            LoggingConstants.formatError(
              LoggingConstants.authModule,
              LoggingConstants.recoverableError,
              'Failed to save user to local storage: $error',
              'User: ${user.email}',
            ),
            error,
          );
        });
      }

      return user;
    });
  }

  /// Gets the current user.
  @override
  UserModel get currentUser {
    final firebaseUser = _firebaseAuth.currentUser;
    if (firebaseUser != null) {
      return _mapFirebaseUserToUserModel(firebaseUser);
    }

    // Try to get from local storage if Firebase user is null
    final localUser = _hiveService.getCurrentUser();
    return localUser ?? const UserModel.empty();
  }

  @override
  Future<void> logInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      email,
      'Firebase authentication initiated',
    );

    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final duration = DateTime.now().difference(startTime);
      final user = _mapFirebaseUserToUserModel(credential.user!);

      // Save to local storage
      await _hiveService.saveUser(user);

      _logger
        ..logPerformance(
          'Firebase login authentication',
          duration,
          'Email: $email',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          email,
          'Firebase authentication successful',
        );
    } on FirebaseAuthException catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        email,
        'Firebase authentication error: ${e.code} - ${e.message}',
      );

      throw LogInWithEmailAndPasswordFailure.fromCode(e.code);
    } catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        email,
        'Unexpected error during login: $e',
      );

      throw const LogInWithEmailAndPasswordFailure();
    }
  }

  @override
  Future<void> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.signupAttempt,
      'started',
      email,
      'Firebase registration initiated for: $name',
    );

    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await credential.user?.updateDisplayName(name);
      await credential.user?.reload();

      final duration = DateTime.now().difference(startTime);
      final user = _mapFirebaseUserToUserModel(credential.user!);

      // Save to local storage
      await _hiveService.saveUser(user);

      _logger
        ..logPerformance(
          'Firebase signup registration',
          duration,
          'Email: $email, Name: $name',
        )
        ..logAuth(
          LoggingConstants.signupSuccess,
          'completed',
          email,
          'Firebase registration successful for: $name',
        );
    } on FirebaseAuthException catch (e) {
      _logger.logAuth(
        LoggingConstants.signupFailure,
        'failed',
        email,
        'Firebase registration error: ${e.code} - ${e.message}',
      );

      throw SignUpWithEmailAndPasswordFailure.fromCode(e.code);
    } catch (e) {
      _logger.logAuth(
        LoggingConstants.signupFailure,
        'failed',
        email,
        'Unexpected error during signup: $e',
      );

      throw const SignUpWithEmailAndPasswordFailure();
    }
  }

  @override
  Future<void> sendPasswordResetEmail({
    required String email,
  }) async {
    _logger.logAuth(
      LoggingConstants.passwordResetAttempt,
      'started',
      email,
      'Firebase password reset initiated',
    );

    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);

      _logger.logAuth(
        LoggingConstants.passwordResetSuccess,
        'completed',
        email,
        'Firebase password reset email sent',
      );
    } on FirebaseAuthException catch (e) {
      _logger.logAuth(
        LoggingConstants.passwordResetFailure,
        'failed',
        email,
        'Firebase password reset error: ${e.code} - ${e.message}',
      );

      throw SendPasswordResetEmailFailure.fromCode(e.code);
    } catch (e) {
      _logger.logAuth(
        LoggingConstants.passwordResetFailure,
        'failed',
        email,
        'Unexpected error during password reset: $e',
      );

      throw const SendPasswordResetEmailFailure();
    }
  }

  @override
  Future<void> logInWithGoogle() async {
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      'google_sign_in',
      'Google Sign-In authentication initiated',
    );

    try {
      // Trigger the authentication flow
      final googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        _logger.logAuth(
          LoggingConstants.loginFailure,
          'cancelled',
          'google_sign_in',
          'Google Sign-In cancelled by user',
        );
        throw const LogInWithGoogleFailure('Sign in cancelled by user');
      }

      // Obtain the auth details from the request
      final googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );

      final duration = DateTime.now().difference(startTime);
      final user = _mapFirebaseUserToUserModel(userCredential.user!);

      // Save to local storage
      await _hiveService.saveUser(user);

      _logger
        ..logPerformance(
          'Google Sign-In authentication',
          duration,
          'Email: ${user.email}',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          user.email,
          'Google Sign-In authentication successful',
        );
    } on FirebaseAuthException catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'google_sign_in',
        'Firebase Google Sign-In error: ${e.code} - ${e.message}',
      );

      throw LogInWithGoogleFailure.fromCode(e.code);
    } catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'google_sign_in',
        'Unexpected error during Google Sign-In: $e',
      );

      throw const LogInWithGoogleFailure();
    }
  }

  @override
  Future<void> logInWithApple() async {
    final startTime = DateTime.now();

    _logger.logAuth(
      LoggingConstants.loginAttempt,
      'started',
      'apple_sign_in',
      'Apple Sign-In authentication initiated',
    );

    try {
      // Request Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        // Add webAuthenticationOptions for Android/Web platforms
        webAuthenticationOptions: kIsWeb || Platform.isAndroid
            ? WebAuthenticationOptions(
                clientId: 'com.algomash.bloomg.bloomg-flutter',
                redirectUri: Uri.parse(
                  'https://bloomg-flutter.web.app/__/auth/handler',
                ),
              )
            : null,
      );

      // Create OAuth credential for Firebase
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in to Firebase with the Apple credential
      final userCredential = await _firebaseAuth.signInWithCredential(
        oauthCredential,
      );

      final duration = DateTime.now().difference(startTime);
      final user = _mapFirebaseUserToUserModel(userCredential.user!);

      // Save to local storage
      await _hiveService.saveUser(user);

      _logger
        ..logPerformance(
          'Apple Sign-In authentication',
          duration,
          'Email: ${user.email}',
        )
        ..logAuth(
          LoggingConstants.loginSuccess,
          'completed',
          user.email,
          'Apple Sign-In authentication successful',
        );
    } on SignInWithAppleAuthorizationException catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'apple_sign_in',
        'Apple Sign-In authorization error: ${e.code} - ${e.message}',
      );

      // Handle specific Apple Sign-In errors
      if (e.code == AuthorizationErrorCode.canceled) {
        throw const LogInWithAppleFailure('Sign in cancelled by user');
      } else {
        throw LogInWithAppleFailure('Apple Sign-In failed: ${e.message}');
      }
    } on FirebaseAuthException catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'apple_sign_in',
        'Firebase Apple Sign-In error: ${e.code} - ${e.message}',
      );

      throw LogInWithAppleFailure.fromCode(e.code);
    } catch (e) {
      _logger.logAuth(
        LoggingConstants.loginFailure,
        'failed',
        'apple_sign_in',
        'Unexpected error during Apple Sign-In: $e',
      );

      throw const LogInWithAppleFailure();
    }
  }

  @override
  Future<void> logOut() async {
    _logger.logAuth(
      LoggingConstants.logoutAttempt,
      'started',
      currentUser.email,
      'Firebase logout initiated',
    );

    try {
      // Clear local storage first
      await _hiveService.clearAllAuthData();

      // Then sign out from Firebase
      await _firebaseAuth.signOut();

      _logger.logAuth(
        LoggingConstants.logoutSuccess,
        'completed',
        '',
        'Firebase logout and local storage cleared',
      );
    } catch (e) {
      _logger.logAuth(
        LoggingConstants.logoutFailure,
        'failed',
        currentUser.email,
        'Firebase logout error: $e',
      );

      throw LogOutFailure();
    }
  }

  /// Maps a [User] from Firebase Auth to our [UserModel].
  UserModel _mapFirebaseUserToUserModel(User firebaseUser) {
    return UserModel(
      uid: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
      emailVerified: firebaseUser.emailVerified,
      createdAt: firebaseUser.metadata.creationTime,
      lastSignInAt: firebaseUser.metadata.lastSignInTime,
    );
  }
}
